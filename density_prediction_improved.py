#!/usr/bin/env python3
"""
Improved Density Prediction Pipeline

Key improvements based on the successful Vp model:
1. Proper data normalization with DensityDataNormalizer
2. Physical constraint loss function (DensityLoss)
3. Data augmentation with windowing
4. Better model architecture selection
5. Enhanced evaluation metrics

Usage:
    python density_prediction_improved.py --train_file A1.hdf5 --test_file A2.hdf5
"""

import os
import sys
import time
import argparse
import logging
import h5py
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

# Import existing model architectures
from model import MWLT_Small, MWLT_Base, MWLT_Large
from utils import EarlyStopping, load_checkpoint, save_checkpoint, cal_RMSE, cal_R2


class DensityDataNormalizer:
    """
    Data normalizer for density prediction with proper scaling and physical constraints
    Based on the successful VpDataNormalizer approach
    """
    
    def __init__(self):
        # Input curve statistics (based on typical well log ranges)
        self.input_stats = {
            'GR': {'mean': 75.0, 'std': 40.0, 'min': 0.0, 'max': 200.0},
            'CNL': {'mean': 0.15, 'std': 0.10, 'min': 0.0, 'max': 0.6},
            'DEN': {'mean': 2.4, 'std': 0.3, 'min': 1.8, 'max': 3.0},
            'RLLD': {'mean': 10.0, 'std': 50.0, 'min': 0.1, 'max': 1000.0}  # Will be log-transformed
        }
        
        # Density statistics (typical range for sedimentary rocks)
        self.density_stats = {
            'mean': 2.4,
            'std': 0.3,
            'min': 1.8,   # Minimum realistic density (g/cc)
            'max': 3.0    # Maximum realistic density (g/cc)
        }
    
    def normalize_inputs(self, curves_dict: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        Normalize input curves to [-1, 1] range
        """
        normalized = {}
        
        for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
            if curve_name in curves_dict:
                data = curves_dict[curve_name].copy()
                
                if curve_name == 'RLLD':
                    # Log transform for resistivity (similar to Vp model)
                    data = np.log10(np.clip(data, 0.1, None))
                    # Normalize log-transformed data
                    normalized[curve_name] = (data - 1.0) / 2.0  # Assuming log range [0.1, 1000] -> [-1, 3]
                    normalized[curve_name] = np.clip(normalized[curve_name], -1, 1)
                else:
                    # Standard normalization for other curves
                    stats = self.input_stats[curve_name]
                    data_norm = (data - stats['mean']) / stats['std']
                    # Clip to [-3, 3] sigma and scale to [-1, 1]
                    normalized[curve_name] = np.clip(data_norm, -3, 3) / 3.0
            else:
                # Create zeros if curve is missing
                normalized[curve_name] = np.zeros_like(list(curves_dict.values())[0])
                
        return normalized
    
    def normalize_density(self, density: np.ndarray) -> np.ndarray:
        """
        Normalize density values using standard normalization
        """
        return (density - self.density_stats['mean']) / self.density_stats['std']
    
    def denormalize_density(self, normalized_density: np.ndarray) -> np.ndarray:
        """
        Convert normalized density back to physical units (g/cc)
        """
        return normalized_density * self.density_stats['std'] + self.density_stats['mean']
    
    def validate_density_range(self, density: np.ndarray) -> np.ndarray:
        """
        Ensure density values are within realistic physical range
        """
        return np.clip(density, self.density_stats['min'], self.density_stats['max'])

    def get_normalization_params(self):
        """Return normalization parameters for validation"""
        return {
            'input_stats': self.input_stats,
            'density_stats': self.density_stats
        }


class DensityLoss(nn.Module):
    """
    Custom loss function for density prediction with physical constraints
    Based on the successful VpLoss approach
    
    Note: This loss function works with NORMALIZED density values.
    The physical constraints are converted to normalized space.
    """
    
    def __init__(self, constraint_weight: float = 0.1, density_range: Tuple[float, float] = (1.8, 3.0),
                 normalizer_stats: Dict = None):
        super().__init__()
        self.mse_loss = nn.MSELoss()
        self.constraint_weight = constraint_weight
        
        # Convert physical density range to normalized range
        if normalizer_stats is None:
            # Default normalization stats
            mean, std = 2.4, 0.3
        else:
            mean = normalizer_stats['mean']
            std = normalizer_stats['std']
            
        # Convert physical constraints to normalized space
        self.min_density_norm = (density_range[0] - mean) / std
        self.max_density_norm = (density_range[1] - mean) / std
        
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Calculate combined MSE + physical constraint loss
        Note: predictions and targets are in normalized space
        """
        # Primary MSE loss
        mse = self.mse_loss(predictions, targets)
        
        # Physical constraint penalty (in normalized space)
        # Penalize predictions outside realistic density range
        constraint_loss = torch.mean(
            torch.relu(predictions - self.max_density_norm) +  # Penalty for too high density
            torch.relu(self.min_density_norm - predictions)    # Penalty for too low density
        )
        
        # Combined loss
        total_loss = mse + self.constraint_weight * constraint_loss
        
        return total_loss


class ImprovedDensityDataset(Dataset):
    """
    Improved dataset class with data augmentation and proper normalization
    """
    
    def __init__(self, file_path: str, input_curves: List[str], output_curves: List[str],
                 normalizer: DensityDataNormalizer, sequence_length: int = 640,
                 augment: bool = True, window_step: int = 160):
        self.file_path = file_path
        self.input_curves = input_curves
        self.output_curves = output_curves
        self.normalizer = normalizer
        self.sequence_length = sequence_length
        self.augment = augment
        self.window_step = window_step
        
        self.samples = []
        self._load_and_process_data()
    
    def _load_and_process_data(self):
        """
        Load HDF5 data and create training samples with augmentation
        """
        print(f"Loading data from: {self.file_path}")
        
        with h5py.File(self.file_path, 'r') as f:
            # Load input curves directly from file root
            input_data = []
            for curve in self.input_curves:
                if curve in f:
                    curve_data = f[curve][:]
                    input_data.append(curve_data)
                else:
                    print(f"Warning: Curve {curve} not found in file")
                    return  # Return early if any required curve is missing
            
            # Load output curves directly from file root
            output_data = []
            for curve in self.output_curves:
                if curve in f:
                    curve_data = f[curve][:]
                    output_data.append(curve_data)
                else:
                    print(f"Warning: Curve {curve} not found in file")
                    return  # Return early if any required curve is missing
            
            # Check if we have all required curves
            if len(input_data) == len(self.input_curves) and len(output_data) == len(self.output_curves):
                # Squeeze and stack the data properly
                # Each curve has shape (1, depth), we need (depth, n_curves)
                input_arrays = [curve.squeeze() for curve in input_data]  # Remove extra dimension
                output_arrays = [curve.squeeze() for curve in output_data]  # Remove extra dimension
                
                input_array = np.column_stack(input_arrays)  # [depth, n_input_curves]
                output_array = np.column_stack(output_arrays)  # [depth, n_output_curves]
                
                data_length = len(input_array)
                print(f"Data length: {data_length}")
                
                # Create windowed samples for data augmentation
                if self.augment and data_length > self.sequence_length:
                    # Create overlapping windows
                    for start_idx in range(0, data_length - self.sequence_length + 1, self.window_step):
                        end_idx = start_idx + self.sequence_length
                        
                        # Extract sequences
                        input_seq = input_array[start_idx:end_idx]  # [seq_len, n_input_curves]
                        output_seq = output_array[start_idx:end_idx]  # [seq_len, n_output_curves]
                        
                        self.samples.append((input_seq, output_seq))
                else:
                    # Single sample (no augmentation)
                    if data_length >= self.sequence_length:
                        input_seq = input_array[:self.sequence_length]
                        output_seq = output_array[:self.sequence_length]
                        self.samples.append((input_seq, output_seq))
                    else:
                        print(f"Warning: Data too short ({data_length} < {self.sequence_length})")
        
        print(f"Created {len(self.samples)} samples")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        input_seq, output_seq = self.samples[idx]
        
        # Convert to dictionary format for normalization
        input_curves = {}
        for i, curve_name in enumerate(self.input_curves):
            input_curves[curve_name] = input_seq[:, i]  # Extract column for each curve
        
        # Normalize inputs
        normalized_inputs = self.normalizer.normalize_inputs(input_curves)
        
        # Normalize output (assuming single output curve)
        normalized_output = self.normalizer.normalize_density(output_seq[:, 0])
        
        # Convert to tensors and transpose to [C, L] format
        input_tensor = torch.stack([
            torch.FloatTensor(normalized_inputs[curve_name]) 
            for curve_name in self.input_curves
        ])  # Shape: [C, L]
        
        output_tensor = torch.FloatTensor(normalized_output).unsqueeze(0)  # Shape: [1, L]
        
        return input_tensor, output_tensor


class ImprovedDensityPipeline:
    """
    Improved density prediction pipeline with enhanced training and evaluation
    """
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device(f"cuda:{args.device}" if torch.cuda.is_available() else "cpu")
        self.normalizer = DensityDataNormalizer()
        self.model = None
        
        # Setup logging
        self.setup_logging()
        
        # Create save directory with proper permissions
        self._create_save_directory(args.save_path)
        
        print(f"Using device: {self.device}")
        print(f"Save path: {args.save_path}")

    def _create_save_directory(self, save_path):
        """
        Create save directory with proper permissions and error handling.
        Falls back to local directory if OneDrive path has persistent issues.
        """
        import stat
        import tempfile

        original_save_path = save_path

        try:
            os.makedirs(save_path, exist_ok=True)

            # Check if directory is writable and fix permissions if needed
            if os.path.exists(save_path) and not os.access(save_path, os.W_OK):
                print(f"Warning: Directory {save_path} is not writable. Attempting to fix permissions...")
                try:
                    if os.name == 'nt':  # Windows
                        # Remove read-only attribute
                        os.chmod(save_path, stat.S_IWRITE | stat.S_IREAD)
                    else:  # Unix-like systems
                        os.chmod(save_path, 0o755)
                    print("Directory permissions updated successfully")
                except Exception as perm_error:
                    print(f"Warning: Could not update directory permissions: {perm_error}")
                    # Test if we can actually write to the directory
                    test_file = os.path.join(save_path, "write_test.tmp")
                    try:
                        with open(test_file, 'w') as f:
                            f.write("test")
                        os.remove(test_file)
                        print("Directory write test passed despite permission warning")
                    except Exception as write_error:
                        print(f"Directory write test failed: {write_error}")
                        raise Exception(f"Cannot write to directory {save_path}")

            # Final write test
            test_file = os.path.join(save_path, "write_test.tmp")
            try:
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                print(f"Save directory ready: {save_path}")
            except Exception as write_error:
                print(f"Final write test failed for {save_path}: {write_error}")
                raise Exception(f"Cannot write to directory {save_path}")

        except Exception as e:
            print(f"Error with save directory {save_path}: {e}")

            # Fallback to local directory if original path has issues
            if "OneDrive" in save_path or ".." in save_path:
                fallback_path = os.path.join(os.getcwd(), "density_results_local")
                print(f"Attempting fallback to local directory: {fallback_path}")

                try:
                    os.makedirs(fallback_path, exist_ok=True)
                    test_file = os.path.join(fallback_path, "write_test.tmp")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)

                    print(f"Fallback directory created successfully: {fallback_path}")
                    # Update the args to use the fallback path
                    self.args.save_path = fallback_path
                    return

                except Exception as fallback_error:
                    print(f"Fallback directory also failed: {fallback_error}")

            raise Exception(f"Could not create any usable save directory. Original error: {e}")

    def setup_logging(self):
        """
        Setup logging configuration
        """
        # Ensure save directory exists before creating log file
        self._create_save_directory(self.args.save_path)
        
        log_file = os.path.join(self.args.save_path, "training.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_model(self):
        """
        Create model based on specified architecture
        """
        input_channels = len(self.args.input_curves)
        output_channels = len(self.args.output_curves)
        
        if self.args.model_type == "small":
            model = MWLT_Small(
                in_channels=input_channels,
                out_channels=output_channels,
                feature_num=64,
                dim=64,
                seq_len=self.args.sequence_length
            )
        elif self.args.model_type == "base":
            model = MWLT_Base(
                in_channels=input_channels,
                out_channels=output_channels,
                feature_num=64,
                dim=64,
                seq_len=self.args.sequence_length
            )
        elif self.args.model_type == "large":
            model = MWLT_Large(
                in_channels=input_channels,
                out_channels=output_channels,
                feature_num=128,
                dim=128,
                seq_len=self.args.sequence_length
            )
        else:
            raise ValueError(f"Unknown model type: {self.args.model_type}")
        
        self.model = model.to(self.device)
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        self.logger.info(f"Model created: {self.args.model_type}")
        self.logger.info(f"Total parameters: {total_params:,}")
        self.logger.info(f"Trainable parameters: {trainable_params:,}")
        
        return self.model
    
    def train_model(self):
        """
        Enhanced training with improved loss function and data handling
        """
        self.logger.info("Starting improved training...")
        
        # Create datasets with augmentation
        train_dataset = ImprovedDensityDataset(
            file_path=self.args.train_file,
            input_curves=self.args.input_curves,
            output_curves=self.args.output_curves,
            normalizer=self.normalizer,
            sequence_length=self.args.sequence_length,
            augment=True,  # Enable data augmentation for training
            window_step=self.args.window_step
        )
        
        val_dataset = ImprovedDensityDataset(
            file_path=self.args.val_file,
            input_curves=self.args.input_curves,
            output_curves=self.args.output_curves,
            normalizer=self.normalizer,
            sequence_length=self.args.sequence_length,
            augment=False  # No augmentation for validation
        )
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=self.args.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)
        
        self.logger.info(f"Training samples: {len(train_dataset)}")
        self.logger.info(f"Validation samples: {len(val_dataset)}")
        
        # Create model, optimizer, and improved loss function
        model = self.create_model()
        optimizer = optim.Adam(model.parameters(), lr=self.args.learning_rate, weight_decay=1e-5)
        criterion = DensityLoss(
            constraint_weight=0.1,
            normalizer_stats=self.normalizer.density_stats
        ).to(self.device)
        
        # Learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=10
        )
        
        # Early stopping
        model_path = os.path.join(self.args.save_path, "best_density_improved_model.pth")
        early_stopping = EarlyStopping(patience=self.args.patience, path=model_path)
        
        # Training loop
        training_history = {"train_loss": [], "val_loss": []}
        
        self.logger.info(f"Starting training for {self.args.epochs} epochs...")
        training_start_time = time.time()
        
        for epoch in range(1, self.args.epochs + 1):
            epoch_start_time = time.time()
            
            # Training phase
            model.train()
            train_total_loss = 0.0
            
            for batch_idx, (inputs, targets) in enumerate(train_loader):
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)
                
                optimizer.zero_grad()
                predictions = model(inputs)
                loss = criterion(predictions, targets)
                loss.backward()
                
                # Gradient clipping for stability
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_total_loss += loss.item()
            
            train_loss_avg = train_total_loss / len(train_loader)
            
            # Validation phase
            model.eval()
            val_total_loss = 0.0
            
            with torch.no_grad():
                for inputs, targets in val_loader:
                    inputs = inputs.to(self.device)
                    targets = targets.to(self.device)
                    
                    predictions = model(inputs)
                    loss = criterion(predictions, targets)
                    val_total_loss += loss.item()
            
            val_loss_avg = val_total_loss / len(val_loader)
            
            # Update learning rate
            scheduler.step(val_loss_avg)
            
            # Log progress
            training_history["train_loss"].append(train_loss_avg)
            training_history["val_loss"].append(val_loss_avg)
            
            epoch_time = time.time() - epoch_start_time
            self.logger.info(
                f"Epoch [{epoch}/{self.args.epochs}] - "
                f"Train Loss: {train_loss_avg:.6f}, "
                f"Val Loss: {val_loss_avg:.6f}, "
                f"LR: {optimizer.param_groups[0]['lr']:.2e}, "
                f"Time: {epoch_time:.2f}s"
            )
            
            # Early stopping check
            state = {
                "model_state_dict": model.state_dict(),
                "loss": val_loss_avg,
                "epoch": epoch
            }
            early_stopping(state, model)
            
            if early_stopping.early_stop:
                self.logger.info("Early stopping triggered")
                break
        
        training_time = time.time() - training_start_time
        self.logger.info(f"Training completed in {training_time:.2f}s")
        
        # Save training history
        history_df = pd.DataFrame(training_history)
        history_df.to_csv(os.path.join(self.args.save_path, "training_history.csv"), index=False)
        
        return model_path
    
    def evaluate_model(self, model_path: str, test_file: str):
        """
        Enhanced model evaluation with proper denormalization and metrics
        """
        self.logger.info("Starting model evaluation...")
        
        # Load model
        if not self.model:
            self.create_model()
        
        model_dict, epoch, loss = load_checkpoint(model_path, self.device)
        self.model.load_state_dict(model_dict)
        self.model.eval()
        
        self.logger.info(f"Loaded model from epoch {epoch} with validation loss {loss:.6f}")
        
        # Create test dataset
        test_dataset = ImprovedDensityDataset(
            file_path=test_file,
            input_curves=self.args.input_curves,
            output_curves=self.args.output_curves,
            normalizer=self.normalizer,
            sequence_length=self.args.sequence_length,
            augment=False
        )
        
        test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)
        
        # Perform evaluation
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for inputs, targets in test_loader:
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)
                
                # Get normalized predictions
                predictions_norm = self.model(inputs)
                
                # Denormalize to physical units
                predictions_phys = self.normalizer.denormalize_density(predictions_norm.cpu().numpy())
                targets_phys = self.normalizer.denormalize_density(targets.cpu().numpy())
                
                # Validate physical range
                predictions_phys = self.normalizer.validate_density_range(predictions_phys)
                
                all_predictions.extend(predictions_phys.flatten())
                all_targets.extend(targets_phys.flatten())
        
        # Calculate metrics
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        rmse = cal_RMSE(all_predictions, all_targets)
        r2 = cal_R2(all_predictions, all_targets)
        
        # Additional metrics
        mae = np.mean(np.abs(all_predictions - all_targets))
        pred_range = [all_predictions.min(), all_predictions.max()]
        target_range = [all_targets.min(), all_targets.max()]
        
        self.logger.info("=== Evaluation Results ===")
        self.logger.info(f"RMSE: {rmse:.4f} g/cc")
        self.logger.info(f"R²: {r2:.4f}")
        self.logger.info(f"MAE: {mae:.4f} g/cc")
        self.logger.info(f"Prediction range: [{pred_range[0]:.3f}, {pred_range[1]:.3f}] g/cc")
        self.logger.info(f"Target range: [{target_range[0]:.3f}, {target_range[1]:.3f}] g/cc")
        
        # Save results
        results = {
            'rmse': rmse,
            'r2': r2,
            'mae': mae,
            'predictions': all_predictions,
            'targets': all_targets,
            'pred_range': pred_range,
            'target_range': target_range
        }
        
        # Save detailed results
        results_file = os.path.join(self.args.save_path, "evaluation_results.npz")
        np.savez(results_file, **results)
        
        return results
    
    def run_pipeline(self):
        """
        Run the complete improved pipeline
        """
        pipeline_start_time = time.time()
        
        try:
            self.logger.info("=== Starting Improved Density Prediction Pipeline ===")
            self.logger.info(f"Input curves: {self.args.input_curves}")
            self.logger.info(f"Output curves: {self.args.output_curves}")
            self.logger.info(f"Model type: {self.args.model_type}")
            
            # Phase 1: Training
            model_path = self.train_model()
            
            # Phase 2: Evaluation
            results = self.evaluate_model(model_path, self.args.test_file)
            
            # Pipeline completion
            pipeline_time = time.time() - pipeline_start_time
            
            self.logger.info("=== Pipeline Completed Successfully! ===")
            self.logger.info(f"Total pipeline time: {pipeline_time:.2f}s")
            self.logger.info(f"Final RMSE: {results['rmse']:.4f} g/cc")
            self.logger.info(f"Final R²: {results['r2']:.4f}")
            
            # Performance assessment
            if results['rmse'] < 0.1 and results['r2'] > 0.8:
                self.logger.info("🎉 EXCELLENT performance!")
            elif results['rmse'] < 0.2 and results['r2'] > 0.5:
                self.logger.info("✅ GOOD performance!")
            else:
                self.logger.info("⚠️ Performance needs improvement")
            
            return {
                "model_path": model_path,
                "results": results,
                "pipeline_time": pipeline_time
            }
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            raise


def parse_arguments():
    """
    Parse command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Improved Density Prediction Pipeline",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # File paths
    parser.add_argument("--train_file", type=str, default="A1.hdf5",
                       help="Training HDF5 file")
    parser.add_argument("--val_file", type=str, default="A1.hdf5",
                       help="Validation HDF5 file")
    parser.add_argument("--test_file", type=str, default="A2.hdf5",
                       help="Test HDF5 file")
    parser.add_argument("--save_path", type=str, default="../density_improved_results",
                       help="Directory to save results")
    
    # Model configuration
    parser.add_argument("--model_type", type=str, default="base", choices=["small", "base", "large"],
                       help="Model architecture")
    parser.add_argument("--input_curves", nargs='+', default=["GR", "CNL", "DEN", "RLLD"],
                       help="Input curve names")
    parser.add_argument("--output_curves", nargs='+', default=["DEN"],
                       help="Output curve names")
    
    # Training parameters
    parser.add_argument("--epochs", type=int, default=100, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=16, help="Batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--patience", type=int, default=20, help="Early stopping patience")
    
    # Data parameters
    parser.add_argument("--sequence_length", type=int, default=640, help="Sequence length")
    parser.add_argument("--window_step", type=int, default=160, help="Window step for augmentation")
    
    # Model architecture parameters
    parser.add_argument("--feature_num", type=int, default=128, help="Feature dimension")
    parser.add_argument("--use_pe", type=bool, default=True, help="Use positional encoding")
    parser.add_argument("--drop", type=float, default=0.1, help="Dropout rate")
    parser.add_argument("--attn_drop", type=float, default=0.1, help="Attention dropout")
    parser.add_argument("--position_drop", type=float, default=0.1, help="Position dropout")
    
    # Device
    parser.add_argument("--device", type=str, default="0", help="GPU device")
    
    return parser.parse_args()


def main():
    """
    Main function
    """
    args = parse_arguments()
    
    # Validate input files
    for file_path in [args.train_file, args.val_file, args.test_file]:
        if not os.path.exists(file_path):
            print(f"Error: File not found: {file_path}")
            return
    
    # Run improved pipeline
    pipeline = ImprovedDensityPipeline(args)
    results = pipeline.run_pipeline()
    
    print("\n=== Improved Density Prediction Completed ===")
    print(f"Results saved to: {args.save_path}")
    print(f"Final RMSE: {results['results']['rmse']:.4f} g/cc")
    print(f"Final R²: {results['results']['r2']:.4f}")


if __name__ == "__main__":
    main()