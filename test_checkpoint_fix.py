#!/usr/bin/env python3
"""
Test script to verify the checkpoint saving fix
"""

import os
import sys
import torch
import tempfile
import shutil

# Add current directory to path
sys.path.append('.')

from utils import save_checkpoint, EarlyStopping

def test_checkpoint_saving():
    """Test the improved checkpoint saving functionality"""
    
    print("=== Testing Checkpoint Saving Fix ===")
    
    # Test 1: Basic save_checkpoint function
    print("\n1. Testing basic save_checkpoint function...")
    
    test_dir = "../test_checkpoint_basic"
    test_path = os.path.join(test_dir, "test_model.pth")
    
    state = {
        'model_state_dict': {'test_param': torch.tensor([1.0, 2.0, 3.0])},
        'epoch': 5,
        'loss': 0.123
    }
    
    try:
        save_checkpoint(state, test_path)
        print("✅ Basic checkpoint saving successful")
        
        # Verify the file was created and can be loaded
        if os.path.exists(test_path):
            loaded_state = torch.load(test_path, map_location='cpu', weights_only=False)
            assert loaded_state['epoch'] == 5
            assert loaded_state['loss'] == 0.123
            print("✅ Checkpoint loading verification successful")
        else:
            print("❌ Checkpoint file was not created")
            
    except Exception as e:
        print(f"❌ Basic checkpoint saving failed: {e}")
    finally:
        # Cleanup
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
    
    # Test 2: EarlyStopping with checkpoint saving
    print("\n2. Testing EarlyStopping checkpoint saving...")
    
    test_dir = "../test_checkpoint_early_stopping"
    test_path = os.path.join(test_dir, "best_model.pth")
    
    try:
        # Create a mock model (just a simple tensor for testing)
        mock_model = torch.nn.Linear(2, 1)
        
        # Create EarlyStopping instance
        early_stopping = EarlyStopping(patience=3, path=test_path, verbose=True)
        
        # Simulate training with improving loss
        for epoch in range(3):
            loss = 1.0 - epoch * 0.3  # Decreasing loss
            state = {
                'model_state_dict': mock_model.state_dict(),
                'epoch': epoch + 1,
                'loss': loss
            }
            
            early_stopping(state, mock_model)
            print(f"   Epoch {epoch + 1}: Loss = {loss:.3f}")
        
        # Verify the best model was saved
        if os.path.exists(test_path):
            loaded_state = torch.load(test_path, map_location='cpu', weights_only=False)
            print(f"✅ EarlyStopping saved best model at epoch {loaded_state['epoch']} with loss {loaded_state['loss']:.3f}")
        else:
            print("❌ EarlyStopping did not save checkpoint file")
            
    except Exception as e:
        print(f"❌ EarlyStopping checkpoint saving failed: {e}")
    finally:
        # Cleanup
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
    
    # Test 3: Test with the actual OneDrive path
    print("\n3. Testing with OneDrive path...")
    
    onedrive_path = "../density_improved_results/test_fix_verification.pth"
    
    try:
        save_checkpoint(state, onedrive_path)
        
        if os.path.exists(onedrive_path):
            print("✅ OneDrive path checkpoint saving successful")
            # Cleanup test file
            os.remove(onedrive_path)
        else:
            print("❌ OneDrive path checkpoint file was not created")
            
    except Exception as e:
        print(f"❌ OneDrive path checkpoint saving failed: {e}")
    
    print("\n=== Checkpoint Saving Test Complete ===")

def test_directory_permissions():
    """Test directory permission handling"""
    
    print("\n=== Testing Directory Permission Handling ===")
    
    # Test creating a directory and checking permissions
    test_dir = "../test_permissions"
    
    try:
        os.makedirs(test_dir, exist_ok=True)
        
        # Check if directory is writable
        if os.access(test_dir, os.W_OK):
            print("✅ Test directory is writable")
        else:
            print("❌ Test directory is not writable")
        
        # Test writing a file
        test_file = os.path.join(test_dir, "test_write.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        if os.path.exists(test_file):
            print("✅ File write test successful")
            os.remove(test_file)
        else:
            print("❌ File write test failed")
            
    except Exception as e:
        print(f"❌ Directory permission test failed: {e}")
    finally:
        # Cleanup
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
    
    print("=== Directory Permission Test Complete ===")

if __name__ == "__main__":
    test_checkpoint_saving()
    test_directory_permissions()
    
    print("\n🎉 All tests completed! The checkpoint saving fix should now work properly.")
    print("\nKey improvements made:")
    print("1. Enhanced save_checkpoint() function with better error handling")
    print("2. Automatic directory creation and permission fixing")
    print("3. Detailed error messages for debugging")
    print("4. Fallback mechanism to local directory if OneDrive has issues")
    print("5. Write tests to verify directory accessibility")
