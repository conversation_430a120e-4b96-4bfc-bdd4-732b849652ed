#!/usr/bin/env python3
"""
ML Architecture Verification Script for Initial Density Module

This script comprehensively tests all ML model architectures and components
to ensure the initial_density module can run independently.
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import traceback

def test_model_components():
    """Test individual model components"""
    print("=== Testing Individual Model Components ===")
    
    try:
        from model import (
            Input_Embedding, ResCNN, PositionalEncoding, 
            TransformerEncoder, TransformerBlock, SelfAttention,
            FeedForward, Decoder, MWL_Transformer
        )
        print("✓ All model components imported successfully")
        
        # Test Input_Embedding
        input_emb = Input_Embedding(in_channels=4, res_num=2, feature_num=64)
        test_input = torch.randn(2, 4, 640)
        output = input_emb(test_input)
        print(f"✓ Input_Embedding: {test_input.shape} -> {output.shape}")
        
        # Test PositionalEncoding
        pos_enc = PositionalEncoding(d_model=64, dropout=0.1, seq_len=640)
        test_input = torch.randn(2, 640, 64)
        output = pos_enc(test_input)
        print(f"✓ PositionalEncoding: {test_input.shape} -> {output.shape}")
        
        # Test TransformerEncoder
        transformer_enc = TransformerEncoder(dim=64, num_heads=4)
        test_input = torch.randn(2, 640, 64)
        output = transformer_enc(test_input)
        print(f"✓ TransformerEncoder: {test_input.shape} -> {output.shape}")
        
        # Test Decoder
        decoder = Decoder(res_num=2, out_channels=1, feature_num=64)
        test_input = torch.randn(2, 64, 640)
        output = decoder(test_input)
        print(f"✓ Decoder: {test_input.shape} -> {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model components test failed: {e}")
        traceback.print_exc()
        return False

def test_model_architectures():
    """Test all model architectures"""
    print("\n=== Testing Model Architectures ===")
    
    try:
        from model import MWLT_Small, MWLT_Base, MWLT_Large
        
        # Test input
        test_input = torch.randn(2, 4, 640)  # batch_size=2, channels=4, seq_len=640
        
        # Test MWLT_Small
        model_small = MWLT_Small(in_channels=4, out_channels=1, seq_len=640)
        output_small = model_small(test_input)
        params_small = sum(p.numel() for p in model_small.parameters())
        print(f"✓ MWLT_Small: {test_input.shape} -> {output_small.shape}, {params_small:,} params")
        
        # Test MWLT_Base
        model_base = MWLT_Base(in_channels=4, out_channels=1, seq_len=640)
        output_base = model_base(test_input)
        params_base = sum(p.numel() for p in model_base.parameters())
        print(f"✓ MWLT_Base: {test_input.shape} -> {output_base.shape}, {params_base:,} params")
        
        # Test MWLT_Large
        model_large = MWLT_Large(in_channels=4, out_channels=1, seq_len=640)
        output_large = model_large(test_input)
        params_large = sum(p.numel() for p in model_large.parameters())
        print(f"✓ MWLT_Large: {test_input.shape} -> {output_large.shape}, {params_large:,} params")
        
        # Verify output shapes
        expected_shape = (2, 1, 640)
        assert output_small.shape == expected_shape, f"Small model output shape mismatch: {output_small.shape} != {expected_shape}"
        assert output_base.shape == expected_shape, f"Base model output shape mismatch: {output_base.shape} != {expected_shape}"
        assert output_large.shape == expected_shape, f"Large model output shape mismatch: {output_large.shape} != {expected_shape}"
        
        print("✓ All model architectures working correctly!")
        return True
        
    except Exception as e:
        print(f"✗ Model architectures test failed: {e}")
        traceback.print_exc()
        return False

def test_gpu_compatibility():
    """Test GPU compatibility"""
    print("\n=== Testing GPU Compatibility ===")
    
    try:
        from utils import get_device
        from model import MWLT_Base
        
        device = get_device()
        print(f"✓ Device detected: {device}")
        
        # Test model on device
        model = MWLT_Base(in_channels=4, out_channels=1, seq_len=640)
        model = model.to(device)
        
        test_input = torch.randn(1, 4, 640).to(device)
        with torch.no_grad():
            output = model(test_input)
        
        print(f"✓ Model successfully running on {device}")
        print(f"✓ Input device: {test_input.device}, Output device: {output.device}")
        
        return True
        
    except Exception as e:
        print(f"✗ GPU compatibility test failed: {e}")
        traceback.print_exc()
        return False

def test_training_compatibility():
    """Test training compatibility"""
    print("\n=== Testing Training Compatibility ===")
    
    try:
        from model import MWLT_Small
        from utils import get_device
        import torch.optim as optim
        
        device = get_device()
        
        # Create model and optimizer
        model = MWLT_Small(in_channels=4, out_channels=1, seq_len=640).to(device)
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        
        # Test training step
        model.train()
        test_input = torch.randn(2, 4, 640).to(device)
        test_target = torch.randn(2, 1, 640).to(device)
        
        optimizer.zero_grad()
        output = model(test_input)
        loss = criterion(output, test_target)
        loss.backward()
        optimizer.step()
        
        print(f"✓ Training step successful: Loss = {loss.item():.6f}")
        
        # Test evaluation mode
        model.eval()
        with torch.no_grad():
            eval_output = model(test_input)
        
        print(f"✓ Evaluation mode successful: Output shape = {eval_output.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Training compatibility test failed: {e}")
        traceback.print_exc()
        return False

def test_integration_with_density_module():
    """Test integration with density prediction module"""
    print("\n=== Testing Integration with Density Module ===")
    
    try:
        from density_prediction_improved import DensityDataNormalizer
        from model import MWLT_Base
        from utils import cal_RMSE, cal_R2
        
        # Test normalizer
        normalizer = DensityDataNormalizer()
        params = normalizer.get_normalization_params()
        print("✓ DensityDataNormalizer integration successful")
        
        # Test model with normalizer
        model = MWLT_Base(in_channels=4, out_channels=1, seq_len=640)
        
        # Simulate normalized input
        test_input = torch.randn(1, 4, 640)
        with torch.no_grad():
            normalized_output = model(test_input)
        
        # Test denormalization
        physical_output = normalizer.denormalize_density(normalized_output.numpy())
        validated_output = normalizer.validate_density_range(physical_output)
        
        print(f"✓ Normalization pipeline: {normalized_output.shape} -> {physical_output.shape}")
        print(f"✓ Physical range: [{validated_output.min():.3f}, {validated_output.max():.3f}] g/cc")
        
        # Test metrics
        dummy_pred = np.random.normal(2.5, 0.1, 100)
        dummy_real = np.random.normal(2.5, 0.1, 100)
        rmse = cal_RMSE(dummy_pred, dummy_real)
        r2 = cal_R2(dummy_pred, dummy_real)
        print(f"✓ Metrics integration: RMSE={rmse:.4f}, R²={r2:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main verification function"""
    print("=" * 60)
    print("ML ARCHITECTURE VERIFICATION FOR INITIAL_DENSITY MODULE")
    print("=" * 60)
    
    tests = [
        ("Model Components", test_model_components),
        ("Model Architectures", test_model_architectures),
        ("GPU Compatibility", test_gpu_compatibility),
        ("Training Compatibility", test_training_compatibility),
        ("Density Module Integration", test_integration_with_density_module),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED! The initial_density module is fully functional and independent.")
        print("✅ All ML model architectures are properly included and working.")
        print("✅ The module can run independently without external dependencies.")
        return True
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
